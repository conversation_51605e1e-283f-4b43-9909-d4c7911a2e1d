import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export const userApi = createApi({
  reducerPath: 'userApi',
  baseQuery: fetchBaseQuery({ baseUrl: 'https://api.example.com/' }), //change this to env
  endpoints: builder => ({
    getUser: builder.query<any, string>({
      query: (id) => `users/${id}`,
    }),
  }),
});

// Auto-generated hook
export const { useGetUserQuery } = userApi;
