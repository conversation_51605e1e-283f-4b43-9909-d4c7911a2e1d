'use client';

import LandingPage from '@/screen/landing';
import { useLandingData } from '@/hooks/useLandingData';
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store/store';
import { updateTripConfig, updatePassengers, resetFlightSearchForm, selectPassengerCounts } from '@/slices/flightSearchSlice'
import { useGetAllProductsQuery } from '@/services/dummyApi';

export default function Home() {
  const { filter, chat, recommendations } = useLandingData();

  return (
    <div>
      <LandingPage
        loadingComponents={{
          filter: filter.isLoading,
          chat: chat.isLoading,
          recommendations: recommendations.isLoading,
        }}
      />
    </div>
  );
}
