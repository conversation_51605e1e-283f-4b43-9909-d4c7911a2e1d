
import { useState, useEffect } from 'react';
import Cookies from 'js-cookie';
import PrimaryFilter from '@/components/globalComponents/primaryFilter';
import {
  PrimaryFilterSkeleton,
  RecommendationSkeleton,
  ChatSkeleton,
} from '@/components/loaders';
import { useSession } from 'next-auth/react';
import Recommendation from './Recommendation';
import ChatWithShasa from '@/components/globalComponents/ChatWithShasa';
import { apiRequest } from '../../uitls/api';

interface LandingPageProps {
  isLoading?: boolean;
  loadingComponents?: {
    filter?: boolean;
    chat?: boolean;
    recommendations?: boolean;
  };
}

const LandingPage = ({
  isLoading = false,
  loadingComponents = {},
}: LandingPageProps) => {
  const { data: session } = useSession() ?? { data: null };
  const [componentLoading, setComponentLoading] = useState({
    filter: loadingComponents.filter || isLoading,
    chat: loadingComponents.chat || isLoading,
    recommendations: loadingComponents.recommendations || isLoading,
  });

  const res = async () => {
    const data = await apiRequest({
      method: 'POST',
      url: '/auth/guest-login',
      params: '',
      body: '',
      contentType: '',
      tokens: ``,
    });
    return data;
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!session || !Cookies.get('guest_id')) {
        try {
          const guestRes = await res(); // Call the API only if session is not available

          if (
            guestRes &&
            guestRes.detail &&
            guestRes.detail.status === 'success'
          ) {
            const { guest_id } = guestRes.detail.data;

            // Store the guest information in localStorage
            Cookies.set('guest_id', guest_id);

            // Optionally store the entire guest data in localStorage as JSON
            // localStorage.setItem('guest_data', JSON.stringify(guestRes.detail.data));
          }
        } catch (error) {
          // console.error("Error during guest login:", error);
        }
      }
    };

    fetchData(); // Fetch data when component mounts or session changes
  }, [Cookies.get('guest_id'), session?.user]);

  // Simulate staggered loading for better UX
  useEffect(() => {
    if (isLoading) {
      // Filter loads first
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, filter: false }));
      }, 800);

      // Chat loads second
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, chat: false }));
      }, 1200);

      // Recommendations load last
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, recommendations: false }));
      }, 1600);
    }
  }, [isLoading]);

  return (
    <div className="p-3 sm:p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      <div className="">
        <p className="text-sm sm:text-base text-default-1000 opacity-75">
          Welcome to NxVoy!
        </p>
        <div className="text-2xl sm:text-4xl font-semibold text-subtitle">
          <span className="text-gradient">Meet Shasa</span>, Your Travel
          Companion!
        </div>
      </div>

      {/* Primary Filter Section */}
      <div className="py-3 sm:py-4">
        {componentLoading.filter ? (
          <PrimaryFilterSkeleton />
        ) : (
          <PrimaryFilter />
        )}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-3 sm:gap-4 flex-1 min-h-0">
        {/* Chat Section */}
        <div className="lg:col-span-3 order-2 lg:order-1">
          {componentLoading.chat ? <ChatSkeleton /> : <ChatWithShasa />}
        </div>

        {/* Recommendations Section */}
        <div className="lg:col-span-2 order-1 lg:order-2">
          {componentLoading.recommendations ? (
            <RecommendationSkeleton cardCount={3} />
          ) : (
            <Recommendation />
          )}
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
