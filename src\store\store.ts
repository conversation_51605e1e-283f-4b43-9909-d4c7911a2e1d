import { configureStore } from '@reduxjs/toolkit';
import { userApi } from '@/services/userApi';
import { dummyApi } from '@/services/dummyApi';
import flightSearchReducer from "@/slices/flightSearchSlice"

export function makeStore() {
  const store = configureStore({
    reducer: {
      flightSearchForm: flightSearchReducer,
      [userApi.reducerPath]: userApi.reducer, // Add API reducer here
      [dummyApi.reducerPath]: dummyApi.reducer
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(userApi.middleware).concat(dummyApi.middleware), // Add API middleware here
  });
  return store;
}

// Type definitions
export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore['getState']>;
export type AppDispatch = AppStore['dispatch'];
