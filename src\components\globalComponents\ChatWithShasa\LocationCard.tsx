'use client';

import React, { useState } from 'react';
import { Card, CardBody, Button, Chip } from '@heroui/react';
import { FaMapMarkerAlt, FaCalendarAlt, FaClock } from 'react-icons/fa';
import { IoIosArrowUp, IoIosArrowDown } from 'react-icons/io';
import Image from 'next/image';

interface LocationData {
  id: string;
  name: string;
  country: string;
  description: string;
  image: string;
  duration: string;
  bestTime: string;
  activities: string[];
  highlights: string[];
  estimatedCost: {
    currency: string;
    amount: string;
    period: string;
  };
  rating: number;
  tags: string[];
}

interface LocationCardProps {
  location: LocationData;
  onBookClick?: () => void;
  onViewDetailsClick?: () => void;
}

const LocationCard: React.FC<LocationCardProps> = ({
  location,
  onBookClick,
  onViewDetailsClick,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="w-full min-w-md max-w-md mx-auto shadow-lg hover:shadow-xl transition-all duration-300 border-none">
      <CardBody className="p-0">
        {/* Header - Always Visible */}
        <div
          className="cursor-pointer"
          onClick={() => setIsExpanded(!isExpanded)}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              setIsExpanded(!isExpanded);
            }
          }}
          role="button"
          tabIndex={0}
        >
          <div className="relative h-48 w-full">
            <Image
              src={location.image}
              alt={location.name}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

            {/* Location Info Overlay */}
            <div className="absolute bottom-4 left-4 right-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold">{location.name}</h3>
                  <p className="text-sm opacity-90 flex items-center gap-1">
                    <FaMapMarkerAlt size={12} />
                    {location.country}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    <p className="text-lg font-semibold">
                      {location.estimatedCost.currency}
                      {location.estimatedCost.amount}
                    </p>
                    <p className="text-xs opacity-80">
                      {location.estimatedCost.period}
                    </p>
                  </div>
                  {isExpanded ? (
                    <IoIosArrowUp size={20} />
                  ) : (
                    <IoIosArrowDown size={20} />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Info Bar */}
          <div className="p-4 bg-gray-50 flex items-center justify-between text-sm">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1 text-gray-600">
                <FaClock size={12} />
                <span>{location.duration}</span>
              </div>
              <div className="flex items-center gap-1 text-gray-600">
                <FaCalendarAlt size={12} />
                <span>{location.bestTime}</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-yellow-500">★</span>
              <span className="font-medium">{location.rating}</span>
            </div>
          </div>
        </div>

        {/* Expandable Content */}
        <div
          className={`transition-all duration-500 ease-in-out overflow-hidden ${
            isExpanded ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="p-4 bg-blue-400 space-y-4">
            {/* Description */}
            <div>
              <p className="text-gray-700 text-sm leading-relaxed">
                {location.description}
              </p>
            </div>

            {/* Tags */}
            <div>
              <div className="flex flex-wrap gap-2">
                {location.tags.map(tag => (
                  <Chip
                    key={`${location.id}-tag-${tag}`}
                    size="sm"
                    variant="flat"
                    className="bg-blue-100 text-blue-800"
                  >
                    {tag}
                  </Chip>
                ))}
              </div>
            </div>

            {/* Highlights */}
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">
                Top Highlights
              </h4>
              <ul className="space-y-1">
                {location.highlights.slice(0, 3).map(highlight => (
                  <li
                    key={`${location.id}-highlight-${highlight.substring(0, 20)}`}
                    className="text-sm text-gray-600 flex items-start gap-2"
                  >
                    <span className="text-blue-500 mt-1">•</span>
                    {highlight}
                  </li>
                ))}
              </ul>
            </div>

            {/* Activities */}
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">
                Popular Activities
              </h4>
              <div className="flex flex-wrap gap-1">
                {location.activities.slice(0, 4).map(activity => (
                  <Chip
                    key={`${location.id}-activity-${activity}`}
                    size="sm"
                    variant="bordered"
                    className="text-xs"
                  >
                    {activity}
                  </Chip>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <Button
                size="sm"
                color="primary"
                className="flex-1"
                onPress={onBookClick}
              >
                Book Now
              </Button>
              <Button
                size="sm"
                variant="bordered"
                className="flex-1"
                onPress={onViewDetailsClick}
              >
                View Details
              </Button>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default LocationCard;
